<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题 -->
		<view class="header">
			<view class="back-button" @tap="goBack">←</view>
			<text class="header-title">健康档案</text>
		</view>

		<view class="main-content">
			<!-- 基本信息 -->
			<view class="section">
				<text class="section-title">基本信息</text>
				<view class="info-grid">
					<view class="info-item">
						<text class="info-label">姓名</text>
						<text class="info-value">{{ userInfo.name || '张三' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">性别</text>
						<text class="info-value">{{ userInfo.gender || '男' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">年龄</text>
						<text class="info-value">{{ userInfo.age || '35' }}岁</text>
					</view>
					<view class="info-item">
						<text class="info-label">身高</text>
						<text class="info-value">{{ userInfo.height || '175' }}cm</text>
					</view>
					<view class="info-item">
						<text class="info-label">体重</text>
						<text class="info-value">{{ userInfo.weight || '70' }}kg</text>
					</view>
					<view class="info-item">
						<text class="info-label">血型</text>
						<text class="info-value">{{ userInfo.bloodType || 'A型' }}</text>
					</view>
				</view>
				<view class="btn-add" @tap="viewBasicInfo">
					<text>查看详情</text>
				</view>
			</view>

			<!-- 体质辨识 -->
			<view class="section">
				<text class="section-title">体质辨识</text>
				<view class="info-item">
					<text class="info-label">体质类型</text>
					<text class="info-value">{{ constitutionInfo.type || '气虚质' }}</text>
				</view>
				<view class="info-item" style="margin-top: 30rpx;">
					<text class="info-label">主要表现</text>
					<text class="info-value">{{ constitutionInfo.symptoms || '容易疲劳，气短懒言，易出汗，易感冒' }}</text>
				</view>
				<view class="btn-add" @tap="viewConstitution">
					<text>查看详情</text>
				</view>
			</view>

			<!-- 就诊记录 -->
			<view class="section">
				<text class="section-title">就诊记录</text>
				<view class="record-list">
					<view class="record-item" v-for="(record, index) in medicalRecords" :key="index">
						<view class="record-info">
							<text class="record-title">{{ record.title }}</text>
							<text class="record-meta">{{ record.doctor }} - {{ record.department }}</text>
						</view>
						<text class="record-date">{{ record.date }}</text>
					</view>
				</view>
				<view class="btn-add" @tap="viewMedicalRecords">
					<text>查看详情</text>
				</view>
			</view>

			<!-- 用药记录 -->
			<view class="section">
				<text class="section-title">用药记录</text>
				<view class="record-list">
					<view class="record-item" v-for="(medication, index) in medicationRecords" :key="index">
						<view class="record-info">
							<text class="record-title">{{ medication.name }}</text>
							<text class="record-meta">{{ medication.usage }}</text>
						</view>
						<text class="record-date">{{ medication.date }}</text>
					</view>
				</view>
				<view class="btn-add" @tap="viewMedicationRecords">
					<text>查看详情</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				userInfo: {},
				constitutionInfo: {},
				medicalRecords: [],
				medicationRecords: []
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 查看基本信息详情
			viewBasicInfo() {
				uni.navigateTo({
					url: '/pages/basic-info/basic-info'
				});
			},
			
			// 查看体质辨识详情
			viewConstitution() {
				uni.navigateTo({
					url: '/pages/constitution/constitution'
				});
			},
			
			// 查看就诊记录详情
			viewMedicalRecords() {
				uni.navigateTo({
					url: '/pages/medical-records/medical-records'
				});
			},
			
			// 查看用药记录详情
			viewMedicationRecords() {
				uni.navigateTo({
					url: '/pages/medication-records/medication-records'
				});
			},
			
			// 获取用户健康档案数据
			getHealthProfile() {
				let that = this;
				
				// 显示加载提示
				uni.showLoading({
					title: '加载中...'
				});
				
				// 这里使用模拟数据，实际项目中应该调用真实API
				// util.request(api.HealthProfile, {}).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.userInfo = res.data.userInfo;
				// 		that.constitutionInfo = res.data.constitutionInfo;
				// 		that.medicalRecords = res.data.medicalRecords;
				// 		that.medicationRecords = res.data.medicationRecords;
				// 	}
				// 	uni.hideLoading();
				// });
				
				// 模拟数据
				setTimeout(() => {
					that.userInfo = that.getMockUserInfo();
					that.constitutionInfo = that.getMockConstitutionInfo();
					that.medicalRecords = that.getMockMedicalRecords();
					that.medicationRecords = that.getMockMedicationRecords();
					uni.hideLoading();
				}, 1000);
			},
			
			// 获取模拟用户信息
			getMockUserInfo() {
				return {
					name: '张三',
					gender: '男',
					age: '35',
					height: '175',
					weight: '70',
					bloodType: 'A型'
				};
			},
			
			// 获取模拟体质信息
			getMockConstitutionInfo() {
				return {
					type: '气虚质',
					symptoms: '容易疲劳，气短懒言，易出汗，易感冒'
				};
			},
			
			// 获取模拟就诊记录
			getMockMedicalRecords() {
				return [
					{
						title: '脾胃调理',
						doctor: '张医生',
						department: '中医内科',
						date: '2024-03-15'
					},
					{
						title: '颈椎病调理',
						doctor: '李医生',
						department: '针灸科',
						date: '2024-03-10'
					}
				];
			},
			
			// 获取模拟用药记录
			getMockMedicationRecords() {
				return [
					{
						name: '补中益气汤',
						usage: '每日两次，饭后服用',
						date: '2024-03-15'
					},
					{
						name: '四君子汤',
						usage: '每日三次，饭前服用',
						date: '2024-03-10'
					}
				];
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.getHealthProfile();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function(options) {
			this.getHealthProfile();
		}
	}
</script>

<style lang="scss">
	:root {
		--primary-color: #8B4513;
		--secondary-color: #D2B48C;
		--background-color: #FDF5E6;
		--text-color: #4A4A4A;
		--accent-color: #CD853F;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	.container {
		max-width: 750rpx;
		margin: 0 auto;
		background: #fff;
		min-height: 100vh;
		position: relative;
	}

	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.05;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
		background-size: 100rpx 100rpx;
	}

	.header {
		background: linear-gradient(90deg, #f9f6f2 80%, #f3e9d7 100%);
		color: #8B4513;
		padding: 40rpx 30rpx;
		text-align: center;
		position: relative;
		border-radius: 0 0 36rpx 36rpx;
		margin: 0 20rpx 20rpx 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(139,69,19,0.04);
	}

	.header-title {
		font-size: 48rpx;
		letter-spacing: 8rpx;
		font-weight: bold;
		color: #8B4513;
	}

	.back-button {
		position: absolute;
		left: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #8B4513;
		font-size: 36rpx;
		font-weight: bold;
	}

	.main-content {
		padding: 40rpx;
		margin-bottom: 120rpx;
	}

	.section {
		background: #fff;
		border: 4rpx solid #D2B48C;
		border-radius: 16rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
		position: relative;
		overflow: hidden;
	}

	.section::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 8rpx;
		background: linear-gradient(90deg, #8B4513, #D2B48C);
	}

	.section-title {
		font-size: 36rpx;
		color: #8B4513;
		margin-bottom: 30rpx;
		font-weight: bold;
		position: relative;
		padding-left: 30rpx;
	}

	.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: #8B4513;
		border-radius: 4rpx;
	}

	.info-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30rpx;
	}

	.info-item {
		background: #FDF5E6;
		padding: 30rpx;
		border-radius: 8rpx;
		position: relative;
	}

	.info-label {
		color: #666;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.info-value {
		color: #4A4A4A;
		font-weight: bold;
		font-size: 30rpx;
	}

	.record-list {
		margin-top: 30rpx;
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		border-bottom: 2rpx solid #D2B48C;
	}

	.record-item:last-child {
		border-bottom: none;
	}

	.record-info {
		flex: 1;
	}

	.record-title {
		font-weight: bold;
		margin-bottom: 10rpx;
		font-size: 32rpx;
		color: #4A4A4A;
	}

	.record-meta {
		font-size: 28rpx;
		color: #666;
	}

	.record-date {
		color: #666;
		font-size: 28rpx;
	}

	.btn-add {
		display: block;
		width: 100%;
		padding: 20rpx 44rpx;
		background: #fff;
		color: #8B4513;
		border: 3rpx solid #D2B48C;
		border-radius: 44rpx;
		text-align: center;
		margin-top: 30rpx;
		transition: all 0.3s ease;
		font-size: 30rpx;
		font-weight: 500;
		box-shadow: 0 2rpx 8rpx rgba(139,69,19,0.03);
	}

	.btn-add:active {
		background: linear-gradient(90deg, #8B4513 80%, #D2B48C 100%);
		color: #fff;
		border: none;
		box-shadow: 0 4rpx 16rpx rgba(139,69,19,0.10);
	}
</style>
