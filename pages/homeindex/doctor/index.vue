<template>
	<view class="container">
		<view class="decoration"></view>
		
		<!-- 顶部标题区域 -->
		<view class="header">
			<text class="header-title">{{ appTitle }}</text>
		</view>

		<!-- 搜索框区域 -->
		<view class="search-section">
			<input type="text" class="search-box" placeholder="搜索中医好物,养生知识..." v-model="searchKeyword" @confirm="onSearch" />
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 功能导航 -->
			<view class="function-grid">
				<view class="function-card" @tap="navigateTo('/pages/appoint/appoint')">
					<view class="icon">📅</view>
					<text class="function-title">预约咨询</text>
					<text class="function-desc">专业中医师在线咨询</text>
				</view>
				<view class="function-card" @tap="navigateTo('/pages/myhealth/myhealth')">
					<view class="icon">📋</view>
					<text class="function-title">体质检测</text>
					<text class="function-desc">个人健康数据管理</text>
				</view>
				<view class="function-card" @tap="navigateTo('/pages/health/health')">
					<view class="icon">📚</view>
					<text class="function-title">健康话题</text>
					<text class="function-desc">中医养生知识分享</text>
				</view>
				<view class="function-card" @tap="navigateTo('/pages/catalog/catalog')">
					<view class="icon">🛒</view>
					<text class="function-title">中药商城</text>
					<text class="function-desc">优质中药材选购</text>
				</view>
			</view>

			<!-- 健康资讯 -->
			<view class="health-news">
				<text class="section-title">🍃 今日养生</text>
				<view class="news-item" v-for="(item, index) in healthNews" :key="index" @tap="viewNewsDetail(item)">
					<view class="news-icon">{{ item.icon }}</view>
					<view class="news-content">
						<text class="news-title">{{ item.title }}</text>
						<text class="news-desc">{{ item.description }}</text>
					</view>
				</view>
			</view>

			<!-- 商城推荐 -->
			<view class="mall-section">
				<text class="section-title">🏪 精选好物</text>
				<view class="product-grid">
					<view class="product-card" v-for="(product, index) in recommendProducts" :key="index" @tap="viewProductDetail(product)">
						<image class="product-image" :src="product.image" mode="aspectFill"></image>
						<text class="product-name">{{ product.name }}</text>
						<text class="product-price">¥{{ product.price }}</text>
					</view>
				</view>
			</view>

			<!-- 医生推荐 -->
			<view class="doctor-section">
				<text class="section-title">👨‍⚕️ 名医推荐</text>
				<view class="doctor-card" v-for="(doctor, index) in recommendDoctors" :key="index" @tap="viewDoctorDetail(doctor)">
					<image class="doctor-avatar" :src="doctor.avatar" mode="aspectFill"></image>
					<view class="doctor-info">
						<text class="doctor-name">{{ doctor.name }}</text>
						<text class="doctor-specialty">{{ doctor.specialty }}</text>
						<text class="doctor-description">{{ doctor.description }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	
	export default {
		data() {
			return {
				appTitle: '千合商城', // 默认标题，会在 onLoad 中更新
				searchKeyword: '',
				healthNews: [],
				recommendProducts: [],
				recommendDoctors: []
			}
		},
		methods: {
			// 获取首页数据
			getIndexData() {
				uni.showLoading({
					title: '加载中...'
				});
				
				// 获取健康资讯数据
				this.getHealthNews();
				// 获取推荐商品数据
				this.getRecommendProducts();
				// 获取推荐医生数据
				this.getRecommendDoctors();
				
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
			},
			
			// 获取健康资讯
			getHealthNews() {
				let that = this;
				
				// 使用模拟数据，实际项目中可以取消注释下面的API调用
				that.healthNews = that.getMockHealthNews();
				
				// 真实API调用（注释状态）
				// util.request(api.HealthArticles, {
				// 	page: 1,
				// 	size: 3
				// }).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.healthNews = res.data.data;
				// 	}
				// });
			},
			
			// 获取推荐商品
			getRecommendProducts() {
				let that = this;
				
				// 使用模拟数据，实际项目中可以取消注释下面的API调用
				that.recommendProducts = that.getMockProducts();
				
				// 真实API调用（注释状态）
				// util.request(api.IndexUrlHotGoods).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.recommendProducts = res.data.data.slice(0, 4);
				// 	}
				// });
			},
			
			// 获取推荐医生
			getRecommendDoctors() {
				let that = this;
				
				// 使用模拟数据，实际项目中可以取消注释下面的API调用
				that.recommendDoctors = that.getMockDoctors();
				
				// 真实API调用（注释状态）
				// util.request(api.RecommendDoctors).then(function(res) {
				// 	if (res.errno === 0) {
				// 		that.recommendDoctors = res.data.data;
				// 	}
				// });
			},
			
			// 模拟健康资讯数据
			getMockHealthNews() {
				return [
					{
						id: 1,
						icon: '🌱',
						title: '春季养肝正当时',
						description: '春养肝，夏养心，秋养肺，冬养肾，四季养脾胃'
					},
					{
						id: 2,
						icon: '🍵',
						title: '药食同源话养生',
						description: '中医药膳调理，让食物成为最好的药物'
					},
					{
						id: 3,
						icon: '🧘',
						title: '五脏六腑调理法',
						description: '传统中医理论指导现代健康生活'
					}
				];
			},
			
			// 模拟商品数据
			getMockProducts() {
				return [
					{
						id: 1,
						name: '长白山人参',
						price: 298,
						image: '/static/images/noportait.png'
					},
					{
						id: 2,
						name: '宁夏枸杞',
						price: 68,
						image: '/static/images/noportait.png'
					},
					{
						id: 3,
						name: '印尼燕窝',
						price: 1280,
						image: '/static/images/noportait.png'
					},
					{
						id: 4,
						name: '土蜂蜜',
						price: 128,
						image: '/static/images/noportait.png'
					}
				];
			},
			
			// 模拟医生数据
			getMockDoctors() {
				return [
					{
						id: 1,
						name: '张仲景',
						specialty: '中医内科 · 主任医师',
						description: '擅长：脾胃调理、亚健康调理、慢性病管理',
						avatar: '/static/images/noportait.png'
					},
					{
						id: 2,
						name: '李时珍',
						specialty: '中医妇科 · 副主任医师',
						description: '擅长：妇科调理、月经不调、更年期综合征',
						avatar: '/static/images/noportait.png'
					}
				];
			},
			
			// 搜索功能
			onSearch() {
				if (this.searchKeyword.trim()) {
					uni.navigateTo({
						url: `/pages/search/search?keyword=${this.searchKeyword}`
					});
				}
			},
			
			// 页面导航
			navigateTo(url) {
				// 判断是否为 tabBar 页面
				const tabBarPages = [
					'/pages/homeindex/doctor/index',
					'/pages/appoint/appoint',
					'/pages/health/health',
					'/pages/cart/cart',
					'/pages/ucenter/index/index'
				];

				if (tabBarPages.includes(url)) {
					uni.switchTab({
						url: url
					});
				} else {
					uni.navigateTo({
						url: url
					});
				}
			},
			
			// 查看资讯详情
			viewNewsDetail(item) {
				uni.navigateTo({
					url: `/pages/health/health?id=${item.id}`
				});
			},
			
			// 查看商品详情
			viewProductDetail(product) {
				uni.navigateTo({
					url: `/pages/goods/goods?id=${product.id}`
				});
			},
			
			// 查看医生详情
			viewDoctorDetail(doctor) {
				uni.navigateTo({
					url: `/pages/appoint/appoint?doctorId=${doctor.id}`
				});
			}
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.getIndexData();
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		
		onLoad: function() {
			// 获取全局应用标题
			const app = getApp();
			if (app.globalData && app.globalData.appTitle) {
				this.appTitle = app.globalData.appTitle;
			}

			this.getIndexData();
		}
	}
</script>

<style scoped>
	.container {
		width: 100%;
		background: #fff;
		min-height: 100vh;
		position: relative;
		overflow-x: hidden;
	}

	/* 顶部导航 */
	.header {
		background: #8B4513;
		color: #fff;
		padding: 15px;
		text-align: center;
		position: relative;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
		background-size: 20px 20px;
		border-bottom: 2px solid #D2B48C;
	}

	.header-title {
		font-size: 24px;
		letter-spacing: 4px;
		text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
		font-weight: 700;
	}

	/* 主要内容区域 */
	.main-content {
		padding: 0 20px 20px 20px;
	}

	/* 搜索框区域 */
	.search-section {
		padding: 15px 20px;
		background: #fff;
	}

	.search-box {
		width: 100%;
		height: 50px;
		padding: 15px 20px 15px 55px;
		border: 2px solid #D2B48C;
		border-radius: 25px;
		font-size: 15px;
		background: #fff url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23D2B48C"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') no-repeat 20px center;
		background-size: 20px 20px;
		transition: all 0.3s ease;
		box-sizing: border-box;
		color: #4A4A4A;
		line-height: 1.4;
		text-align: left;
	}

	.search-box:focus {
		border-color: #8B4513;
		box-shadow: 0 0 10px rgba(139, 69, 19, 0.2);
		text-align: left;
	}

	/* 占位符文字样式 */
	.search-box::placeholder {
		color: #999;
		font-size: 15px;
		text-align: left;
		line-height: 1.4;
	}

	/* 功能导航网格 */
	.function-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15px;
		margin-bottom: 25px;
	}

	.function-card {
		background: linear-gradient(135deg, #fff 0%, #F5E6D3 100%);
		border-radius: 15px;
		padding: 20px;
		text-align: center;
		box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
		border: 1px solid #D2B48C;
		transition: all 0.3s ease;
	}

	.function-card:active {
		transform: translateY(-3px);
		box-shadow: 0 8px 25px rgba(139, 69, 19, 0.2);
	}

	.function-card .icon {
		width: 50px;
		height: 50px;
		margin: 0 auto 12px;
		background: #8B4513;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-size: 24px;
	}

	.function-title {
		color: #8B4513;
		font-size: 16px;
		margin-bottom: 8px;
		font-weight: 700;
		display: block;
	}

	.function-desc {
		font-size: 12px;
		color: #4A4A4A;
		opacity: 0.8;
		line-height: 1.4;
		display: block;
	}

	/* 健康资讯卡片 */
	.health-news {
		background: #fff;
		border-radius: 15px;
		padding: 20px;
		margin-bottom: 25px;
		box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
		border-left: 4px solid #DAA520;
	}

	.section-title {
		color: #8B4513;
		margin-bottom: 15px;
		font-size: 18px;
		display: flex;
		align-items: center;
		gap: 8px;
		font-weight: 700;
	}

	.news-item {
		display: flex;
		align-items: center;
		padding: 10px 0;
		border-bottom: 1px solid #f0f0f0;
		transition: all 0.3s ease;
	}

	.news-item:last-child {
		border-bottom: none;
	}

	.news-item:active {
		background: #F5E6D3;
		border-radius: 8px;
		padding: 10px;
		margin: 0 -10px;
	}

	.news-icon {
		width: 40px;
		height: 40px;
		background: #D2B48C;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
		font-size: 18px;
		color: #8B4513;
	}

	.news-content {
		flex: 1;
	}

	.news-title {
		color: #8B4513;
		font-size: 14px;
		margin-bottom: 4px;
		display: block;
		font-weight: 600;
	}

	.news-desc {
		font-size: 12px;
		color: #4A4A4A;
		opacity: 0.8;
		display: block;
	}

	/* 商城推荐区域 */
	.mall-section {
		background: #fff;
		border-radius: 15px;
		padding: 20px;
		margin-bottom: 25px;
		box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
	}

	.product-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 12px;
	}

	.product-card {
		background: #F5E6D3;
		border-radius: 10px;
		padding: 12px;
		text-align: center;
		transition: all 0.3s ease;
	}

	.product-card:active {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
	}

	.product-image {
		width: 60px;
		height: 60px;
		border-radius: 8px;
		margin-bottom: 8px;
	}

	.product-name {
		font-size: 14px;
		color: #8B4513;
		margin-bottom: 4px;
		display: block;
		font-weight: 600;
	}

	.product-price {
		font-size: 16px;
		color: #CD853F;
		font-weight: 700;
		display: block;
	}

	/* 医生推荐区域 */
	.doctor-section {
		background: #fff;
		border-radius: 15px;
		padding: 20px;
		margin-bottom: 25px;
		box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
	}

	.doctor-card {
		display: flex;
		align-items: center;
		padding: 15px;
		background: #F5E6D3;
		border-radius: 12px;
		margin-bottom: 12px;
		transition: all 0.3s ease;
	}

	.doctor-card:last-child {
		margin-bottom: 0;
	}

	.doctor-card:active {
		transform: translateX(5px);
		box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
	}

	.doctor-avatar {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		margin-right: 15px;
		border: 3px solid #D2B48C;
	}

	.doctor-info {
		flex: 1;
	}

	.doctor-name {
		color: #8B4513;
		font-size: 16px;
		margin-bottom: 4px;
		display: block;
		font-weight: 600;
	}

	.doctor-specialty {
		font-size: 12px;
		color: #CD853F;
		margin-bottom: 4px;
		display: block;
	}

	.doctor-description {
		font-size: 12px;
		color: #4A4A4A;
		opacity: 0.8;
		display: block;
	}

	/* 装饰元素 */
	.decoration {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		opacity: 0.03;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,10 Q60,30 50,50 Q40,30 50,10 M50,50 Q60,70 50,90 Q40,70 50,50" fill="%238B4513"/><circle cx="20" cy="20" r="3" fill="%23DAA520"/><circle cx="80" cy="80" r="3" fill="%23DAA520"/></svg>');
		background-size: 80px 80px;
	}
</style>
