<template>
	<view class="container">
		<view class="order-info">
			<view class="item-a">下单时间：{{orderInfo.purchaseDate}}</view>
			<view class="item-b">订单编号：{{orderInfo.sellerOrderId}}</view>
			<view class="item-c">
				<view class="l">实付：<text class="cost">￥{{orderInfo.orderMoney}}</text></view>
				<view class="r">
					<view v-if="orderInfo.orderStatus == '0'">
						<view class="btn" @tap="cancelOrder">取消订单</view>
						<view class="btn active" @tap="payOrder">立即支付</view>
					</view>
					<view v-else-if="orderInfo.orderStatus == '2' && orderInfo.receiveStatus !== '1'">
						<view class="btn active" @tap="confirmOrder">确认收货</view>
					</view>
					<view v-else>

					</view>
				</view>
			</view>
		</view>

		<view class="order-goods">
			<view class="h">
				<view class="label">商品信息</view>
				<view class="status">{{orderInfo.amazonOrderStatus}}</view>
			</view>
			<view class="goods" v-for="(item, index) in orderGoods" :key="item.saleOrderDetailId">
				<view class="item" >
					<view class="img">
						<image :src="item.smallImage"></image>
					</view>
					<view class="info">
						<view class="t">
							<text class="name">{{item.title}}</text>
							<text class="number">x{{item.quantityOrdered}}</text>
						</view>
						<view class="price">￥{{item.itemPrice}}</view>
					</view>
				</view>
				<view class="comments">
					<view class="h">
						<text class="t">我要评价</text>
						<image @tap="postComment(item.fbaInventoryId)" class="i" src="/static/images/edit.png"></image>
					</view>
				</view>
			</view>
			
		</view>

		<view class="order-bottom">
			<view class="address">
				<view class="t">
					<text class="name">{{orderInfo.buyerName}}</text>
					<text class="mobile">{{orderInfo.phone}}</text>
				</view>
				<view class="b">{{orderInfo.stateOrRegion+orderInfo.city+orderInfo.district+ orderInfo.buyerAddress1}}</view>
			</view>
			<view class="total">
				<view class="t">
					<text class="label">商品合计：</text>
					<text class="txt">￥{{orderInfo.subTotal}}</text>
				</view>
				<view class="t">
					<text class="label">运费：</text>
					<text class="txt">￥{{orderInfo.shipMoney}}</text>
				</view>
			</view>
			<view class="pay-fee">
				<text class="label">实付：</text>
				<text class="txt">￥{{orderInfo.orderMoney}}</text>
			</view>

		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');
	export default {
		data() {
			return {
				orderId: '',
				orderInfo: {},
				orderGoods: [],
				handleOption: {}
			}
		},
		methods: {
			getOrderDetail() {
				let that = this;
				util.request(api.OrderDetail, {
					orderId: that.orderId
				}).then(function(res) {
					if (res.errno === 0) {
						that.orderInfo = res.data.orderInfo
						that.orderGoods = res.data.orderGoods
						that.handleOption = res.data.handleOption
						//that.payTimer();
					}
				});
			},
			payTimer() {
				let that = this;
				let orderInfo = that.orderInfo;

				setInterval(() => {
					orderInfo.addTime -= 1;
					that.orderInfo = orderInfo;
				}, 1000);
			},
			cancelOrder() {
				let that = this;
				let orderInfo = that.orderInfo;

				var orderStatus = orderInfo.orderStatus;

				var receiveStatus = orderInfo.receiveStatus;
				var returnStatus = orderInfo.returnStatus;
				var shippedStatus = orderInfo.shippedStatus;
				var delFlag = orderInfo.delFlag;

				var errorMessage = '';
				if( shippedStatus == '1'){
					errorMessage = '订单已发货';
				}
				if( receiveStatus == '1'){
					errorMessage = '订单已收货';
				}
				if( delFlag == '1'){
					errorMessage = '订单已取消';
				}
				if( returnStatus !== '0'){
					errorMessage = '订单已退货';
				}

				if (errorMessage != '') {
					util.toast(errorMessage);
					return false;
				}
			

				if (errorMessage != '') {
					util.toast(errorMessage);
					return false;
				}

				uni.showModal({
					title: '',
					content: '确定要取消此订单？',
					success: function(res) {
						if (res.confirm) {
							util.request(api.OrderCancel, {
								orderId: that.orderId
							}).then(function(res) {
								if (res.errno === 0) {
									uni.showModal({
										title: '提示',
										content: res.data,
										showCancel: false,
										confirmText: '继续',
										success: function(res) {
											uni.navigateBack({
												url: 'pages/ucenter/order/order',
											});
										}
									});
								}else{
									util.toast(res.errmsg);
								}
							});

						}
					}
				});
			},
			payOrder() {
				let that = this;
				util.payOrder(parseInt(that.orderId)).then(res => {
					that.getOrderDetail();
				}).catch(res => {
					util.toast('支付失败');
				});
			},
			confirmOrder() {
				//确认收货
				let that = this;
				let orderInfo = that.orderInfo;

				var orderStatus = orderInfo.orderStatus;
				var receiveStatus = orderInfo.receiveStatus;
				var returnStatus = orderInfo.returnStatus;
				var shippedStatus = orderInfo.shippedStatus;
				var delFlag = orderInfo.delFlag;

				var errorMessage = '';
				if( receiveStatus == '1'){
					errorMessage = '订单已收货';
				}
				if( delFlag == '1'){
					errorMessage = '订单已取消';
				}
				if( returnStatus !== '0'){
					errorMessage = '订单已退货';
				}

				if (errorMessage != '') {
					util.toast(errorMessage);
					return false;
				}

				uni.showModal({
					title: '',
					content: '确定已经收到商品？',
					success: function(res) {
						if (res.confirm) {
							util.request(api.OrderConfirm, {
								orderId: that.orderId
							}).then(function(res) {
								if (res.errno === 0) {
									uni.showModal({
										title: '提示',
										content: res.data,
										showCancel: false,
										confirmText: '继续',
										success: function(res) {
											uni.navigateBack({
												url: 'pages/ucenter/order/order',
											});
										}
									});
								}else {
									util.toast(res.errmsg)
								}
							});

						}
					}
				});
			},
			postComment(goodsId) {
				wx.navigateTo({
					url: '/pages/commentPost/commentPost?valueId=' + goodsId + '&typeId=0',
				})
			},
		},
		onLoad: function(options) {
			this.orderId = options.id;
			this.getOrderDetail();
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		width: 100%;
		background: #f4f4f4;
	}

	.order-info {
		padding-top: 25rpx;
		background: #fff;
		height: auto;
		overflow: hidden;
	}

	.item-a {
		padding-left: 31.25rpx;
		height: 42.5rpx;
		padding-bottom: 12.5rpx;
		line-height: 30rpx;
		font-size: 30rpx;
		color: #666;
	}

	.item-b {
		padding-left: 31.25rpx;
		height: 29rpx;
		line-height: 29rpx;
		margin-top: 12.5rpx;
		margin-bottom: 41.5rpx;
		font-size: 30rpx;
		color: #666;
	}

	.item-c {
		margin-left: 31.25rpx;
		border-top: 1px solid #f4f4f4;
		height: 103rpx;
		line-height: 103rpx;
	}

	.item-c .l {
		float: left;
	}

	.item-c .r {
		height: 103rpx;
		float: right;
		display: flex;
		align-items: center;
		padding-right: 16rpx;
	}

	.item-c .r .btn {
		float: right;
	}

	.item-c .cost {
		color: #b4282d;
	}

	.item-c .btn {
		line-height: 66rpx;
		border-radius: 5rpx;
		text-align: center;
		margin: 0 15rpx;
		padding: 0 20rpx;
		height: 66rpx;
	}

	.item-c .btn.active {
		background: #b4282d;
		color: #fff;
	}

	.order-goods {
		margin-top: 20rpx;
		background: #fff;
	}

	.order-goods .h {
		height: 93.75rpx;
		line-height: 93.75rpx;
		margin-left: 31.25rpx;
		border-bottom: 1px solid #f4f4f4;
		padding-right: 31.25rpx;
	}

	.order-goods .h .label {
		float: left;
		font-size: 30rpx;
		color: #333;
	}

	.order-goods .h .status {
		float: right;
		font-size: 30rpx;
		color: #b4282d;
	}

	.order-goods .item {
		display: flex;
		align-items: center;
		height: 192rpx;
		margin-left: 31.25rpx;
		padding-right: 31.25rpx;
		border-bottom: 1px solid #f4f4f4;
	}

	.order-goods .item:last-child {
		border-bottom: none;
	}

	.order-goods .item .img {
		height: 145.83rpx;
		width: 145.83rpx;
		background: #f4f4f4;
	}

	.order-goods .item .img image {
		height: 145.83rpx;
		width: 145.83rpx;
	}

	.order-goods .item .info {
		flex: 1;
		height: 145.83rpx;
		margin-left: 20rpx;
	}

	.order-goods .item .t {
		margin-top: 8rpx;
		height: 33rpx;
		line-height: 33rpx;
		margin-bottom: 10.5rpx;
	}

	.order-goods .item .t .name {
		display: block;
		float: left;
		height: 33rpx;
		line-height: 33rpx;
		color: #333;
		font-size: 30rpx;
	}

	.order-goods .item .t .number {
		display: block;
		float: right;
		height: 33rpx;
		text-align: right;
		line-height: 33rpx;
		color: #333;
		font-size: 30rpx;
	}

	.order-goods .item .attr {
		height: 29rpx;
		line-height: 29rpx;
		color: #666;
		margin-bottom: 25rpx;
		font-size: 25rpx;
	}

	.order-goods .item .price {
		height: 30rpx;
		line-height: 30rpx;
		color: #333;
		font-size: 30rpx;
	}

	.order-bottom {
		margin-top: 20rpx;
		padding-left: 31.25rpx;
		height: auto;
		overflow: hidden;
		background: #fff;
	}

	.order-bottom .address {
		height: 128rpx;
		padding-top: 25rpx;
		border-bottom: 1px solid #f4f4f4;
	}

	.order-bottom .address .t {
		height: 35rpx;
		line-height: 35rpx;
		margin-bottom: 7.5rpx;
	}

	.order-bottom .address .name {
		display: inline-block;
		height: 35rpx;
		width: 140rpx;
		line-height: 35rpx;
		font-size: 25rpx;
	}

	.order-bottom .address .mobile {
		display: inline-block;
		height: 35rpx;
		line-height: 35rpx;
		font-size: 25rpx;
	}

	.order-bottom .address .b {
		height: 35rpx;
		line-height: 35rpx;
		font-size: 25rpx;
	}

	.order-bottom .total {
		height: 106rpx;
		padding-top: 20rpx;
		border-bottom: 1px solid #f4f4f4;
	}

	.order-bottom .total .t {
		height: 25rpx;
		line-height: 25rpx;
		margin-bottom: 7.5rpx;
		display: flex;
	}

	.order-bottom .total .label {
		width: 140rpx;
		display: inline-block;
		height: 35rpx;
		line-height: 35rpx;
		font-size: 25rpx;
	}

	.order-bottom .total .txt {
		flex: 1;
		display: inline-block;
		height: 35rpx;
		line-height: 35rpx;
		font-size: 25rpx;
	}

	.order-bottom .pay-fee {
		height: 81rpx;
		line-height: 81rpx;
	}

	.order-bottom .pay-fee .label {
		display: inline-block;
		width: 140rpx;
		color: #b4282d;
	}

	.order-bottom .pay-fee .txt {
		display: inline-block;
		width: 140rpx;
		color: #b4282d;
	}
	

	.comments {
		width: 100%;
		height: auto;
		padding-left: 30rpx;
		background: #fff;
		margin-top: 20rpx;
	}

	.comments .h {
		height: 93rpx;
		line-height: 93rpx;
		width: 720rpx;
		padding-right: 30rpx;
		border-bottom: 1px solid #d9d9d9;
	}

	.comments .h .t {
		display: block;
		float: left;
		width: 50%;
		font-size: 29rpx;
		color: #333;
	}

	.comments .h .i {
		display: block;
		float: right;
		margin-top: 30rpx;
		width: 33rpx;
		height: 33rpx;
	}
</style>
