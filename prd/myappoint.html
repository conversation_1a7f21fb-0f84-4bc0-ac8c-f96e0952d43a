<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的预约 - 好医生</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .main-content {
            padding: 20px;
            margin-bottom: 60px;
        }

        .tab-container {
            display: flex;
            border-bottom: 2px solid var(--secondary-color);
            margin-bottom: 20px;
            background: #fff;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 12px;
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .tab.active {
            color: var(--primary-color);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }

        .appointment-card {
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .appointment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .appointment-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 14px;
            background: var(--background-color);
        }

        .status-pending {
            color: #E6A23C;
            background: #FDF6EC;
        }

        .status-confirmed {
            color: #67C23A;
            background: #F0F9EB;
        }

        .status-completed {
            color: #909399;
            background: #F4F4F5;
        }

        .appointment-info {
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .info-label {
            width: 80px;
            color: #666;
        }

        .appointment-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid var(--secondary-color);
            padding-top: 15px;
        }

        .action-button {
            padding: 8px 16px;
            border: 1px solid var(--secondary-color);
            border-radius: 4px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .action-button.primary {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }

        .action-button:hover {
            background: var(--background-color);
        }

        .action-button.primary:hover {
            background: var(--accent-color);
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
            position: relative;
            padding: 5px 0;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="profile.html" class="back-button">←</a>
            <h1>我的预约</h1>
        </header>

        <main class="main-content">
            <div class="tab-container">
                <div class="tab active">待确认</div>
                <div class="tab">已确认</div>
                <div class="tab">已完成</div>
            </div>

            <div class="appointment-card">
                <div class="appointment-header">
                    <h3>张医生 - 中医内科</h3>
                    <span class="appointment-status status-pending">待确认</span>
                </div>
                <div class="appointment-info">
                    <div class="info-item">
                        <span class="info-label">预约时间：</span>
                        <span>2024-03-20 14:30</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">咨询内容：</span>
                        <span>脾胃调理、亚健康调理</span>
                    </div>
                </div>
                <div class="appointment-actions">
                    <a href="#" class="action-button">取消预约</a>
                    <a href="#" class="action-button primary">联系医生</a>
                </div>
            </div>

            <div class="appointment-card">
                <div class="appointment-header">
                    <h3>李医生 - 针灸科</h3>
                    <span class="appointment-status status-confirmed">已确认</span>
                </div>
                <div class="appointment-info">
                    <div class="info-item">
                        <span class="info-label">预约时间：</span>
                        <span>2024-03-22 10:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">咨询内容：</span>
                        <span>颈椎病调理、经络疏通</span>
                    </div>
                </div>
                <div class="appointment-actions">
                    <a href="#" class="action-button">取消预约</a>
                    <a href="#" class="action-button primary">联系医生</a>
                </div>
            </div>
        </main>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item active">我的</a>
        </nav>
    </div>

    <script>
        // 标签切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html> 