<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医体质评估结果 - TCM Constitution Assessment Results</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f1e8;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            min-height: 100vh;
            color: #333333;
            line-height: 1.6;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 812px;
            position: relative;
            padding: 20px;
        }

        .results-header {
            font-size: 20px;
            font-weight: 500;
            color: #333333;
            margin: 40px 0 20px 0;
        }

        .constitution-display {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
        }

        .body-illustration {
            width: 80px;
            height: 80px;
            background: url('/placeholder.svg?height=80&width=80') no-repeat center;
            background-size: contain;
            margin-right: 20px;
            border: 2px solid #d4a574;
            border-radius: 8px;
            padding: 10px;
        }

        .constitution-label {
            background-color: #d4a574;
            color: #ffffff;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
        }

        .characteristics-section {
            margin-bottom: 40px;
        }

        .characteristic-block {
            margin-bottom: 16px;
            line-height: 1.8;
        }

        .category-label {
            background-color: #d4a574;
            color: #ffffff;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            margin-right: 8px;
        }

        .description-text {
            color: #666666;
            font-size: 16px;
            display: inline;
        }

        .guidance-section {
            margin-top: 40px;
        }

        .section-header {
            font-size: 18px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 20px;
        }

        .expandable-card {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .expandable-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title-container {
            display: flex;
            align-items: center;
        }

        .card-icon {
            width: 24px;
            height: 24px;
            background-color: #d4a574;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
        }

        .chevron {
            width: 20px;
            height: 20px;
            color: #999999;
            transition: transform 0.3s ease;
        }

        .chevron.expanded {
            transform: rotate(90deg);
        }

        .card-content {
            margin-top: 16px;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .card-content.expanded {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main-text {
            color: #666666;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .seasonal-title {
            font-weight: 600;
            color: #333333;
            margin-bottom: 12px;
        }

        .season-item {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
        }

        .season-number {
            position: absolute;
            left: 0;
            top: 0;
            color: #d4a574;
            font-weight: bold;
        }

        .season-text {
            color: #666666;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Responsive design */
        @media (max-width: 375px) {
            .container {
                padding: 15px;
            }
            
            .constitution-display {
                flex-direction: column;
                text-align: center;
            }
            
            .body-illustration {
                margin-right: 0;
                margin-bottom: 15px;
            }
        }

        /* Accessibility improvements */
        .expandable-card:focus {
            outline: 2px solid #d4a574;
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="results-header">您的体质是：</h1>
        
        <div class="constitution-display">
            <div class="body-illustration" role="img" aria-label="中医经络人体图"></div>
            <div class="constitution-label">平和体质</div>
        </div>

        <div class="characteristics-section">
            <div class="characteristic-block">
                <span class="category-label">形体特征</span>
                <span class="description-text">体型匀称、面色红润</span>
            </div>
            
            <div class="characteristic-block">
                <span class="category-label">心理特征</span>
                <span class="description-text">心态平和、精力充沛</span>
            </div>
            
            <div class="characteristic-block">
                <span class="category-label">体质特征</span>
                <span class="description-text">胃口良好、睡眠安稳、脉和有神、两便正常、目光有神、舌苔颜色正常。</span>
            </div>
            
            <div class="characteristic-block">
                <span class="category-label">日常调理</span>
                <span class="description-text">心态平和，继续保持，适量运动</span>
            </div>
        </div>

        <div class="guidance-section">
            <h2 class="section-header">指导意见</h2>
            
            <div class="expandable-card" tabindex="0" role="button" aria-expanded="false" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="card-title-container">
                        <div class="card-icon">♥</div>
                        <span class="card-title">情志调摄</span>
                    </div>
                    <span class="chevron">▶</span>
                </div>
                <div class="card-content">
                    <p class="main-text">保持心态平和，避免过度情绪波动，适当进行冥想或太极等调节身心的活动。</p>
                </div>
            </div>

            <div class="expandable-card expanded-card" tabindex="0" role="button" aria-expanded="true" onclick="toggleCard(this)">
                <div class="card-header">
                    <div class="card-title-container">
                        <div class="card-icon">🍽</div>
                        <span class="card-title">饮食调养</span>
                    </div>
                    <span class="chevron expanded">▶</span>
                </div>
                <div class="card-content expanded">
                    <p class="main-text">饮食宜粗细粮食合理搭配，多吃五谷杂粮、蔬菜瓜果，少食过于油腻及辛辣食品；不要过饥过饱，也不要进食过冷过烫或不干净食物；注意戒烟限酒。</p>
                    
                    <div class="seasonal-recommendations">
                        <p class="seasonal-title">四时饮食调养：</p>
                        
                        <div class="season-item">
                            <span class="season-number">①</span>
                            <p class="season-text"><strong>春</strong>宜多食蔬菜，如菠菜、芹菜、春笋、苋菜等</p>
                        </div>
                        
                        <div class="season-item">
                            <span class="season-number">②</span>
                            <p class="season-text"><strong>夏</strong>宜多食新鲜水果，如西瓜、番茄、菠萝等，其他清凉生津食品，如金银花、菊花、鲜芦根、绿豆、冬瓜、苦瓜、黄瓜、生菜、豆芽等均可酌情食用，以清热祛暑</p>
                        </div>
                        
                        <div class="season-item">
                            <span class="season-number">③</span>
                            <p class="season-text"><strong>长夏</strong>宜选用茯苓、霍香、山药、莲子、薏仁、扁豆、丝瓜等利湿健脾之品。不宜进食滋腻碍胃的食物</p>
                        </div>
                        
                        <div class="season-item">
                            <span class="season-number">④</span>
                            <p class="season-text"><strong>秋</strong>宜选用寒温偏性不明显的平性药食。同时，宜食用滋润滋阴之品以保护阴津，如沙参、麦冬、阿胶、甘草等</p>
                        </div>
                        
                        <div class="season-item">
                            <span class="season-number">⑤</span>
                            <p class="season-text"><strong>冬</strong>宜选用温补之品，如生姜、肉桂、羊肉等温补之品</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleCard(card) {
            const content = card.querySelector('.card-content');
            const chevron = card.querySelector('.chevron');
            const isExpanded = content.classList.contains('expanded');
            
            if (isExpanded) {
                content.classList.remove('expanded');
                chevron.classList.remove('expanded');
                card.setAttribute('aria-expanded', 'false');
            } else {
                content.classList.add('expanded');
                chevron.classList.add('expanded');
                card.setAttribute('aria-expanded', 'true');
            }
        }

        // Keyboard accessibility
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                const focusedElement = document.activeElement;
                if (focusedElement.classList.contains('expandable-card')) {
                    e.preventDefault();
                    toggleCard(focusedElement);
                }
            }
        });

        // Initialize expanded state for dietary therapy card
        document.addEventListener('DOMContentLoaded', function() {
            const expandedCard = document.querySelector('.expanded-card');
            if (expandedCard) {
                const content = expandedCard.querySelector('.card-content');
                const chevron = expandedCard.querySelector('.chevron');
                content.classList.add('expanded');
                chevron.classList.add('expanded');
            }
        });
    </script>
</body>
</html>