<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康圈子 - 好医生</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .main-content {
            padding: 20px;
            margin-bottom: 60px;
        }

        .tab-container {
            display: flex;
            border-bottom: 1px solid var(--secondary-color);
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .post-card {
            background: #fff;
            border: 1px solid var(--secondary-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            border: 1px solid var(--secondary-color);
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: bold;
            color: var(--primary-color);
        }

        .post-time {
            font-size: 12px;
            color: #666;
        }

        .post-content {
            margin-bottom: 15px;
        }

        .post-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 15px;
        }

        .post-image {
            width: 100%;
            aspect-ratio: 1;
            object-fit: cover;
            border-radius: 4px;
        }

        .post-actions {
            display: flex;
            justify-content: space-around;
            border-top: 1px solid var(--secondary-color);
            padding-top: 10px;
        }

        .action-button {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-color);
            text-decoration: none;
            font-size: 14px;
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.1;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 1px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
        }

        .nav-item.active {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="index.html" class="back-button">←</a>
            <h1>健康圈子</h1>
        </header>

        <main class="main-content">
            <div class="tab-container">
                <div class="tab active">推荐</div>
                <div class="tab">关注</div>
                <div class="tab">热门</div>
            </div>

            <div class="post-card">
                <div class="post-header">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="user-avatar">
                    <div class="user-info">
                        <div class="user-name">王医生</div>
                        <div class="post-time">2小时前</div>
                    </div>
                </div>
                <div class="post-content">
                    今天给大家分享一个简单的养生小妙招：每天早晚各按摩涌泉穴5分钟，可以改善睡眠质量，缓解疲劳。具体方法如下...
                </div>
                <div class="post-images">
                    <img src="https://via.placeholder.com/100" alt="养生图片" class="post-image">
                    <img src="https://via.placeholder.com/100" alt="养生图片" class="post-image">
                    <img src="https://via.placeholder.com/100" alt="养生图片" class="post-image">
                </div>
                <div class="post-actions">
                    <a href="#" class="action-button">
                        <span>👍</span>
                        <span>128</span>
                    </a>
                    <a href="#" class="action-button">
                        <span>💬</span>
                        <span>36</span>
                    </a>
                    <a href="#" class="action-button">
                        <span>↗️</span>
                        <span>分享</span>
                    </a>
                </div>
            </div>

            <div class="post-card">
                <div class="post-header">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="user-avatar">
                    <div class="user-info">
                        <div class="user-name">李医生</div>
                        <div class="post-time">5小时前</div>
                    </div>
                </div>
                <div class="post-content">
                    最近很多患者咨询关于春季养生的问题，我整理了一些实用的建议，希望对大家有帮助...
                </div>
                <div class="post-actions">
                    <a href="#" class="action-button">
                        <span>👍</span>
                        <span>96</span>
                    </a>
                    <a href="#" class="action-button">
                        <span>💬</span>
                        <span>24</span>
                    </a>
                    <a href="#" class="action-button">
                        <span>↗️</span>
                        <span>分享</span>
                    </a>
                </div>
            </div>
        </main>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item active">圈子</a>
            <a href="profile.html" class="nav-item">我的</a>
        </nav>
    </div>

    <script>
        // 标签切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html> 