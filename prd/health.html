<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康知识 - 好医生</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .main-content {
            padding: 20px;
        }

        .category-tabs {
            display: flex;
            gap: 16px;
            padding: 18px 10px 10px 10px;
            background: linear-gradient(90deg, #f9f6f2 80%, #f3e9d7 100%);
            border-radius: 18px;
            margin: 0 10px 18px 10px;
            box-shadow: 0 2px 8px rgba(139,69,19,0.04);
            overflow-x: auto;
        }

        .category-tab {
            min-width: 64px;
            padding: 8px 22px;
            border-radius: 22px;
            border: 1.5px solid var(--secondary-color);
            background: #fff;
            color: var(--primary-color);
            font-size: 16px;
            font-family: 'Noto Serif SC', serif;
            font-weight: 500;
            margin-right: 4px;
            transition: all 0.2s;
            box-shadow: 0 1px 4px rgba(139,69,19,0.03);
            cursor: pointer;
            position: relative;
        }

        .category-tab:last-child {
            margin-right: 0;
        }

        .category-tab.active {
            background: linear-gradient(90deg, #8B4513 80%, #D2B48C 100%);
            color: #fff;
            border: none;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(139,69,19,0.10);
        }

        .category-tab:hover:not(.active) {
            background: #f5e9d7;
            border-color: var(--primary-color);
        }

        .article-card {
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .article-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .article-title {
            font-size: 18px;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: bold;
            position: relative;
            padding-left: 15px;
        }

        .article-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .article-meta {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #666;
            font-size: 14px;
        }

        .article-author {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .author-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 5px;
            border: 1px solid var(--secondary-color);
        }

        .article-content {
            color: var(--text-color);
            margin-bottom: 15px;
            line-height: 1.8;
        }

        .article-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid var(--secondary-color);
            padding-top: 10px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
        }

        .action-button {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-color);
            text-decoration: none;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            background: var(--background-color);
        }

        .read-count {
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
            position: relative;
            padding: 5px 0;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="index.html" class="back-button">←</a>
            <h1>健康知识</h1>
        </header>

        <main class="main-content">
            <div class="category-tabs">
                <div class="category-tab active">全部</div>
                <div class="category-tab">养生保健</div>
                <div class="category-tab">中医理论</div>
                <div class="category-tab">食疗药膳</div>
                <div class="category-tab">经络穴位</div>
                <div class="category-tab">常见疾病</div>
            </div>

            <div class="article-card">
                <h3 class="article-title">春季养生之道：顺应自然，调养肝气</h3>
                <div class="article-meta">
                    <span>张医生</span>
                    <span>2024-03-15</span>
                </div>
                <p class="article-content">
                    春季养生重在"养肝"，肝主疏泄，喜条达而恶抑郁。春季万物复苏，阳气生发，此时养生应当顺应自然，调养肝气。本文将从起居、饮食、运动等方面详细介绍春季养生要点...
                </p>
                <div class="article-footer">
                    <div class="interaction-buttons">
                        <a href="#" class="interaction-button">
                            <span>👍</span>
                            <span>128</span>
                        </a>
                        <a href="#" class="interaction-button">
                            <span>💬</span>
                            <span>36</span>
                        </a>
                    </div>
                    <span>阅读 2.3k</span>
                </div>
            </div>

            <div class="article-card">
                <h3 class="article-title">中医体质辨识与调理指南</h3>
                <div class="article-meta">
                    <span>李医生</span>
                    <span>2024-03-14</span>
                </div>
                <p class="article-content">
                    中医将人体体质分为九种基本类型，包括平和质、气虚质、阳虚质、阴虚质、痰湿质、湿热质、血瘀质、气郁质和特禀质。了解自己的体质类型，才能更好地进行养生保健...
                </p>
                <div class="article-footer">
                    <div class="interaction-buttons">
                        <a href="#" class="interaction-button">
                            <span>👍</span>
                            <span>96</span>
                        </a>
                        <a href="#" class="interaction-button">
                            <span>💬</span>
                            <span>24</span>
                        </a>
                    </div>
                    <span>阅读 1.8k</span>
                </div>
            </div>
        </main>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item active">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item">我的</a>
        </nav>
    </div>

    <script>
        // 分类标签切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html> 