<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 好医生</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .main-content {
            padding: 20px;
            margin-bottom: 60px;
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 15px;
            border: 3px solid var(--secondary-color);
        }

        .profile-name {
            font-size: 20px;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .profile-bio {
            color: #666;
            font-size: 14px;
        }

        .menu-section {
            background: #fff;
            border: 1px solid var(--secondary-color);
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--secondary-color);
            text-decoration: none;
            color: var(--text-color);
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .menu-text {
            flex: 1;
        }

        .menu-arrow {
            color: #999;
        }

        .health-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #fff;
            border: 1px solid var(--secondary-color);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.1;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 1px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
        }

        .nav-item.active {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="index.html" class="back-button">←</a>
            <h1>个人中心</h1>
        </header>

        <main class="main-content">
            <div class="profile-header">
                <img src="https://via.placeholder.com/100" alt="用户头像" class="profile-avatar">
                <h2 class="profile-name">张三</h2>
                <p class="profile-bio">中医养生爱好者</p>
            </div>

            <div class="health-stats">
                <div class="stat-card">
                    <div class="stat-value">12</div>
                    <div class="stat-label">咨询次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">28</div>
                    <div class="stat-label">健康积分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">5</div>
                    <div class="stat-label">收藏文章</div>
                </div>
            </div>

            <div class="menu-group">
                <a href="my-appointments.html" class="menu-item">
                    <span class="menu-icon">📅</span>
                    <span>我的预约</span>
                    <span class="menu-arrow">></span>
                </a>
                <a href="my-favorites.html" class="menu-item">
                    <span class="menu-icon">⭐</span>
                    <span>我的收藏</span>
                    <span class="menu-arrow">></span>
                </a>
                <a href="health-record.html" class="menu-item">
                    <span class="menu-icon">📋</span>
                    <span>健康档案</span>
                    <span class="menu-arrow">></span>
                </a>
            </div>

            <div class="menu-group">
                <a href="settings.html" class="menu-item">
                    <span class="menu-icon">⚙️</span>
                    <span>设置</span>
                    <span class="menu-arrow">></span>
                </a>
            </div>
        </main>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item active">我的</a>
        </nav>
    </div>
</body>
</html> 