<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康档案 - 好医生</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .main-content {
            padding: 20px;
            margin-bottom: 60px;
        }

        .section {
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .section-title {
            font-size: 18px;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
            padding-left: 15px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .info-item {
            background: var(--background-color);
            padding: 15px;
            border-radius: 4px;
            position: relative;
        }

        .info-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .info-value {
            color: var(--text-color);
            font-weight: bold;
        }

        .record-list {
            margin-top: 15px;
        }

        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--secondary-color);
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-info {
            flex: 1;
        }

        .record-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .record-meta {
            font-size: 14px;
            color: #666;
        }

        .record-date {
            color: #666;
            font-size: 14px;
        }

        .btn-add {
            display: block;
            width: 100%;
            padding: 12px;
            background: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 4px;
            text-align: center;
            text-decoration: none;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background: var(--accent-color);
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
            position: relative;
            padding: 5px 0;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="profile.html" class="back-button">←</a>
            <h1>健康档案</h1>
        </header>

        <main class="main-content">
            <section class="section">
                <h2 class="section-title">基本信息</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">姓名</div>
                        <div class="info-value">张三</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">性别</div>
                        <div class="info-value">男</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">年龄</div>
                        <div class="info-value">35岁</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">身高</div>
                        <div class="info-value">175cm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">体重</div>
                        <div class="info-value">70kg</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">血型</div>
                        <div class="info-value">A型</div>
                    </div>
                </div>
                <a href="basic-info.html" class="btn-add" style="margin-top:10px;">查看详情</a>
            </section>

            <section class="section">
                <h2 class="section-title">体质辨识</h2>
                <div class="info-item">
                    <div class="info-label">体质类型</div>
                    <div class="info-value">气虚质</div>
                </div>
                <div class="info-item" style="margin-top: 15px;">
                    <div class="info-label">主要表现</div>
                    <div class="info-value">容易疲劳，气短懒言，易出汗，易感冒</div>
                </div>
                <a href="constitution.html" class="btn-add" style="margin-top:10px;">查看详情</a>
            </section>

            <section class="section">
                <h2 class="section-title">就诊记录</h2>
                <div class="record-list">
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-title">脾胃调理</div>
                            <div class="record-meta">张医生 - 中医内科</div>
                        </div>
                        <div class="record-date">2024-03-15</div>
                    </div>
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-title">颈椎病调理</div>
                            <div class="record-meta">李医生 - 针灸科</div>
                        </div>
                        <div class="record-date">2024-03-10</div>
                    </div>
                </div>
                <a href="medical-records.html" class="btn-add" style="margin-top:10px;">查看详情</a>
            </section>

            <section class="section">
                <h2 class="section-title">用药记录</h2>
                <div class="record-list">
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-title">补中益气汤</div>
                            <div class="record-meta">每日两次，饭后服用</div>
                        </div>
                        <div class="record-date">2024-03-15</div>
                    </div>
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-title">四君子汤</div>
                            <div class="record-meta">每日三次，饭前服用</div>
                        </div>
                        <div class="record-date">2024-03-10</div>
                    </div>
                </div>
                <a href="medication-records.html" class="btn-add" style="margin-top:10px;">查看详情</a>
            </section>
        </main>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item active">我的</a>
        </nav>
    </div>
</body>
</html> 