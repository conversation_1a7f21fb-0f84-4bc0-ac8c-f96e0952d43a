<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCM Constitution Assessment Questionnaire</title>
    <style>
        body {
            margin: 0;
            font-family: sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0; /* Just for body background */
        }
        .mobile-screen {
            width: 375px;
            height: 812px;
            background-color: #f5f1e8;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4"><circle cx="1" cy="1" r="0.5" fill="%23e0e0e0"/><circle cx="3" cy="3" r="0.5" fill="%23e0e0e0"/></svg>'); /* subtle_texture pattern */
            background-size: 4px 4px;
        }
        .instruction-header {
            position: absolute;
            top: 60px;
            left: 20px;
            width: 335px;
            font-family: "Chinese_Traditional", serif; /* Fallback to serif */
            font-size: 16px;
            line-height: 24px;
            color: #666666;
            text-align: left;
            margin-bottom: 20px;
        }
        .question-container {
            position: absolute;
            top: 140px;
            width: 375px;
            height: 580px;
            overflow-y: auto;
            padding: 0 20px;
            box-sizing: border-box;
        }
        .question-card {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .question-card.transparent {
            background-color: transparent;
            box-shadow: none;
            padding: 20px 0px;
        }
        .question-text {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .options-container {
            display: flex;
            flex-direction: row;
            gap: 20px; /* Adjust gap as needed */
            flex-wrap: wrap; /* Allow wrapping if not enough space */
        }
        .radio-option label {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #555;
            cursor: pointer;
        }
        .radio-option input[type="radio"] {
            margin-right: 10px;
            accent-color: #8b4513; /* Default accent color */
        }
        .pill-button-group {
            display: flex;
            gap: 10px;
        }
        .pill-button {
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid #cccccc;
            background-color: #ffffff;
            color: #666666;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .pill-button.selected {
            background-color: #d4a574;
            color: #8b4513;
            border-color: #d4a574;
        }
        .submit-button {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 335px;
            height: 50px;
            background-color: #d4a574;
            border-radius: 25px;
            color: #8b4513;
            font-size: 18px;
            font-weight: medium;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(212,165,116,0.3);
            transition: background-color 0.3s ease, transform 0.1s ease;
        }
        .submit-button:hover {
            background-color: #c09060; /* Darken on hover */
        }
        .submit-button:active {
            transform: translateX(-50%) scale(0.98); /* Scale down on tap */
        }
    </style>
</head>
<body>
    <div class="mobile-screen">
        <div class="instruction-header">
            以下问题请根据您近一年的体验和感觉回答，孕妇及18岁以下人群不建议使用本测试。
        </div>

        <div class="question-container">
            <div id="question_1" class="question-card">
                <p class="question-text">1. 您是否每天精力都很充沛？</p>
                <div class="pill-button-group">
                    <button class="pill-button" data-question="question_1" data-value="yes">是</button>
                    <button class="pill-button selected" data-question="question_1" data-value="no">否</button>
                </div>
            </div>

            <div id="question_2" class="question-card transparent">
                <p class="question-text">2. 您是否心态平和？</p>
                <div class="pill-button-group">
                    <button class="pill-button" data-question="question_2" data-value="yes">是</button>
                    <button class="pill-button selected" data-question="question_2" data-value="no">否</button>
                </div>
            </div>

            <div id="question_3" class="question-card transparent">
                <p class="question-text">3. 您是否面色很红润？</p>
                <div class="pill-button-group">
                    <button class="pill-button" data-question="question_3" data-value="yes">是</button>
                    <button class="pill-button selected" data-question="question_3" data-value="no">否</button>
                </div>
            </div>

            <div id="question_4" class="question-card">
                <p class="question-text">4. 您是否经常感到闷闷不乐？</p>
                <div class="pill-button-group">
                    <button class="pill-button selected" data-question="question_4" data-value="yes">是</button>
                    <button class="pill-button" data-question="question_4" data-value="no">否</button>
                </div>
            </div>

            <div id="question_5" class="question-card transparent">
                <p class="question-text">5. 您是否经常忘事，丢三落四的？</p>
                <div class="pill-button-group">
                    <button class="pill-button selected" data-question="question_5" data-value="yes">是</button>
                    <button class="pill-button" data-question="question_5" data-value="no">否</button>
                </div>
            </div>

            <div id="question_6" class="question-card">
                <p class="question-text">6. 您是否经常容易心慌慌？</p>
                <div class="pill-button-group">
                    <button class="pill-button selected" data-question="question_6" data-value="yes">是</button>
                    <button class="pill-button" data-question="question_6" data-value="no">否</button>
                </div>
            </div>

            <div id="question_7" class="question-card transparent">
                <p class="question-text">7. 是否需要进行体质调理师一对一私人咨询？</p>
                <div class="pill-button-group">
                    <button class="pill-button selected" data-question="question_7" data-value="yes">需要</button>
                    <button class="pill-button" data-question="question_7" data-value="no">不需要</button>
                </div>
            </div>
        </div>

        <button class="submit-button">查看结果</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Handle radio button selections
            document.querySelectorAll('input[type="radio"]').forEach(radio => {
                radio.addEventListener('change', (event) => {
                    // No special action needed here, browser handles exclusive selection
                });
            });

            // Handle pill button selections
            document.querySelectorAll('.pill-button').forEach(button => {
                button.addEventListener('click', () => {
                    const questionName = button.dataset.question;
                    document.querySelectorAll(`.pill-button[data-question="${questionName}"]`).forEach(btn => {
                        btn.classList.remove('selected');
                    });
                    button.classList.add('selected');
                });
            });

            // Handle submit button
            document.querySelector('.submit-button').addEventListener('click', () => {
                alert('Form submitted! (Functionality not fully implemented in this prototype)');
                // In a real application, you would collect answers and send them to a backend.
            });
        });
    </script>
</body>
</html>
