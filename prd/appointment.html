<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约咨询 - 好医生</title>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .back-button {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            text-decoration: none;
        }

        .form-container {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--primary-color);
            font-weight: bold;
            position: relative;
            padding-left: 15px;
        }

        .form-group label::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--secondary-color);
            border-radius: 4px;
            font-family: 'Noto Serif SC', serif;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(139, 69, 19, 0.1);
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        .btn-submit {
            background: var(--primary-color);
            color: #fff;
            border: none;
            padding: 15px 30px;
            border-radius: 4px;
            width: 100%;
            font-size: 18px;
            cursor: pointer;
            font-family: 'Noto Serif SC', serif;
            transition: all 0.3s ease;
        }

        .btn-submit:hover {
            background: var(--accent-color);
        }

        .time-slots {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .time-slot {
            padding: 10px;
            border: 1px solid var(--secondary-color);
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .time-slot:hover {
            background: var(--background-color);
        }

        .time-slot.selected {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }

        .time-slot.disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .remaining-slots {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .time-slot.selected .remaining-slots {
            color: #fff;
        }

        .time-slot.disabled .remaining-slots {
            color: #999;
        }

        .date-picker {
            margin-bottom: 20px;
        }

        .date-picker-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .date-picker-title {
            font-size: 16px;
            color: var(--primary-color);
            font-weight: bold;
        }

        .date-picker-nav {
            display: flex;
            gap: 10px;
        }

        .date-picker-nav button {
            background: none;
            border: 1px solid var(--secondary-color);
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .date-picker-nav button:hover {
            background: var(--background-color);
        }

        .date-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }

        .date-cell {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--secondary-color);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-cell:hover {
            background: var(--background-color);
        }

        .date-cell.selected {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }

        .date-cell.disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .date-cell .day {
            font-size: 12px;
            color: #666;
        }

        .date-cell.selected .day {
            color: #fff;
        }

        .date-cell.disabled .day {
            color: #999;
        }

        .date-cell .date {
            font-size: 16px;
            font-weight: bold;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 4px;
            font-family: 'Noto Serif SC', serif;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .submit-btn:hover::after {
            transform: translateX(100%);
        }

        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }

        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
            position: relative;
            padding: 5px 0;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <header class="header">
            <a href="index.html" class="back-button">←</a>
            <h1>预约咨询</h1>
        </header>

        <div class="form-container">
            <form>
                <div class="form-group">
                    <label>姓名</label>
                    <input type="text" class="form-control" placeholder="请输入您的姓名">
                </div>

                <div class="form-group">
                    <label>手机号码</label>
                    <input type="tel" class="form-control" placeholder="请输入您的手机号码">
                </div>

                <div class="form-group">
                    <label>推荐人验证码</label>
                    <input type="text" class="form-control" placeholder="请输入推荐人验证码">
                </div>

                <div class="date-picker">
                    <div class="date-picker-header">
                        <span class="date-picker-title">选择日期</span>
                        <div class="date-picker-nav">
                            <button type="button" class="next-week">下周</button>
                        </div>
                    </div>
                    <div class="date-grid">
                        <div class="date-cell">
                            <span class="day">周一</span>
                            <span class="date">18</span>
                        </div>
                        <div class="date-cell">
                            <span class="day">周二</span>
                            <span class="date">19</span>
                        </div>
                        <div class="date-cell selected">
                            <span class="day">周三</span>
                            <span class="date">20</span>
                        </div>
                        <div class="date-cell">
                            <span class="day">周四</span>
                            <span class="date">21</span>
                        </div>
                        <div class="date-cell">
                            <span class="day">周五</span>
                            <span class="date">22</span>
                        </div>
                        <div class="date-cell disabled">
                            <span class="day">周六</span>
                            <span class="date">23</span>
                        </div>
                        <div class="date-cell disabled">
                            <span class="day">周日</span>
                            <span class="date">24</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>预约时间</label>
                    <div class="time-slots">
                        <div class="time-slot">
                            <div>09:00</div>
                            <div class="remaining-slots">剩余3个名额</div>
                        </div>
                        <div class="time-slot">
                            <div>10:00</div>
                            <div class="remaining-slots">剩余2个名额</div>
                        </div>
                        <div class="time-slot">
                            <div>11:00</div>
                            <div class="remaining-slots">剩余1个名额</div>
                        </div>
                        <div class="time-slot selected">
                            <div>14:00</div>
                            <div class="remaining-slots">剩余4个名额</div>
                        </div>
                        <div class="time-slot">
                            <div>15:00</div>
                            <div class="remaining-slots">剩余3个名额</div>
                        </div>
                        <div class="time-slot disabled">
                            <div>16:00</div>
                            <div class="remaining-slots">已约满</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>咨询需求</label>
                    <textarea class="form-control" placeholder="请详细描述您的咨询需求..."></textarea>
                </div>

                <button type="submit" class="btn-submit">提交预约</button>
            </form>
        </div>

        <nav class="nav">
            <a href="index.html" class="nav-item">首页</a>
            <a href="appointment.html" class="nav-item active">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item">我的</a>
        </nav>
    </div>

    <script>
        // 日期选择
        document.querySelectorAll('.date-cell:not(.disabled)').forEach(cell => {
            cell.addEventListener('click', function() {
                document.querySelectorAll('.date-cell').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 时间选择
        document.querySelectorAll('.time-slot:not(.disabled)').forEach(slot => {
            slot.addEventListener('click', function() {
                document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 只保留下周按钮
        document.querySelector('.next-week').addEventListener('click', function() {
            // 实现下周切换逻辑
        });
    </script>
</body>
</html> 