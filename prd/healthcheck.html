<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Traditional Chinese Medicine Constitution Assessment</title>
    <style>
        body {
            margin: 0;
            font-family: sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0; /* Just for body background */
        }
        .mobile-screen {
            width: 375px;
            height: 812px;
            background-color: #f8f8f8;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><circle cx="1" cy="1" r="1" fill="%23e0e0e0"/><circle cx="6" cy="6" r="1" fill="%23e0e0e0"/></svg>'); /* subtle_geometric pattern */
            background-size: 10px 10px;
        }
        .header-title {
            position: absolute;
            width: 100%;
            top: 80px;
            font-family: "Chinese_Traditional", serif; /* Fallback to serif */
            font-size: 24px;
            font-weight: bold;
            color: #2c2c2c;
            text-align: center;
        }
        .constitution-grid {
            position: absolute;
            top: 180px;
            left: 50%;
            transform: translateX(-50%);
            width: 320px;
            height: 320px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 20px;
            justify-items: center;
            align-items: center;
        }
        .circular-button {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #d4a574;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            color: #8b4513;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }
        .circular-button.inactive {
            opacity: 0.7;
        }
        .circular-button.highlight {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .circular-button.active {
            border: 3px solid #8b4513;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: scale(1.05);
        }
        .circular-button .primary-text {
            font-size: 16px;
            font-weight: bold;
        }
        .circular-button .secondary-text {
            font-size: 12px;
        }
        /* Specific styling for the balanced constitution button */
        #balanced_constitution {
            width: 100px;
            height: 100px;
            border: 3px solid #8b4513;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-weight: bold;
        }
        #balanced_constitution .primary-text {
            font-size: 18px;
        }
        #balanced_constitution .secondary-text {
            font-size: 14px;
        }

        .description-text {
            position: absolute;
            top: 550px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            font-family: "Chinese_Traditional", serif;
            font-size: 16px;
            line-height: 24px;
            color: #666666;
            text-align: center;
        }
        .test-button {
            position: absolute;
            top: 680px;
            left: 50%;
            transform: translateX(-50%);
            width: 280px;
            height: 50px;
            background-color: #d4a574;
            border-radius: 25px;
            color: #8b4513;
            font-size: 18px;
            font-weight: medium;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(212,165,116,0.3);
            transition: background-color 0.3s ease, transform 0.1s ease;
        }
        .test-button:hover {
            background-color: #c09060; /* Darken on hover */
        }
        .test-button:active {
            transform: translateX(-50%) scale(0.98); /* Scale down on tap */
        }
    </style>
</head>
<body>
    <div class="mobile-screen">
        <h1 class="header-title">您知道自己属于哪种体质吗？</h1>

        <div class="constitution-grid">
            <div id="qi_deficiency" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">气虚</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="phlegm_dampness" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">痰湿</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="qi_stagnation" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">气郁</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="special_constitution" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">特禀</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="balanced_constitution" class="circular-button active" style="width: 100px; height: 100px;">
                <span class="primary-text">平和</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="damp_heat" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">湿热</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="yang_deficiency" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">阳虚</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="yin_deficiency" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">阴虚</span>
                <span class="secondary-text">体质</span>
            </div>
            <div id="blood_stasis" class="circular-button" style="width: 80px; height: 80px;">
                <span class="primary-text">血瘀</span>
                <span class="secondary-text">体质</span>
            </div>
        </div>

        <p class="description-text">体质是您身体健康特征的描述，qing知道自己的体质可以更好的了解自己！</p>

        <button class="test-button">测试我的体质</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const buttons = document.querySelectorAll('.constitution-grid .circular-button');
            const testButton = document.querySelector('.test-button');

            // Set initial active state based on JSON default_selection
            const defaultSelectionId = 'balanced_constitution'; // From JSON
            const defaultSelectedButton = document.getElementById(defaultSelectionId);
            if (defaultSelectedButton) {
                buttons.forEach(btn => btn.classList.remove('active'));
                defaultSelectedButton.classList.add('active');
            }

            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove 'active' from all buttons
                    buttons.forEach(btn => btn.classList.remove('active'));
                    // Add 'active' to the clicked button
                    button.classList.add('active');
                });

                button.addEventListener('mouseenter', () => {
                    if (!button.classList.contains('active')) {
                        button.classList.add('highlight');
                    }
                });

                button.addEventListener('mouseleave', () => {
                    button.classList.remove('highlight');
                });
            });

            testButton.addEventListener('click', () => {
                alert('Navigate to assessment screen (functionality not implemented in this prototype).');
                // In a real app, you would navigate: window.location.href = 'assessment_screen.html';
            });
        });
    </script>
</body>
</html>
