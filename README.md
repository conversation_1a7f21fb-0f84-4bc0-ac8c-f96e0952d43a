# 千合商城 - UniApp跨平台电商应用

## 项目概述

千合商城是一个基于UniApp框架开发的跨平台电商应用，支持微信小程序、H5、APP等多端运行。项目采用Vue.js + UniApp技术栈，提供完整的电商购物功能，包括商品展示、购物车、订单管理、用户中心等核心功能。

## 功能特性

### 🛍️ 商品功能
- **首页展示**: 轮播图、分类导航、品牌推荐、专题精选、新品首发、人气推荐
- **商品分类**: 多级分类展示，支持分类筛选和搜索
- **商品详情**: 商品信息展示、规格选择、加入购物车、立即购买
- **商品搜索**: 关键词搜索、搜索历史、搜索建议
- **商品推荐**: 相关商品推荐、大家都在看

### 🛒 购物功能
- **购物车**: 商品添加、数量修改、规格选择、批量删除
- **订单管理**: 订单创建、支付、确认收货、取消订单
- **地址管理**: 收货地址添加、编辑、删除、设为默认
- **支付功能**: 微信支付、支付宝支付（配置中）

### 👤 用户功能
- **用户认证**: 微信登录、手机号登录、注册、密码重置
- **个人中心**: 用户信息、订单管理、收藏管理、浏览记录
- **优惠券**: 优惠券列表、使用记录
- **意见反馈**: 用户反馈提交
- **预约咨询**: 在线预约咨询服务，支持日期时间选择

### 📱 其他功能
- **品牌展示**: 品牌列表、品牌详情
- **专题活动**: 专题列表、专题详情、专题评论
- **商品评价**: 评价列表、评价发布
- **网络状态**: 网络连接状态监控
- **应用更新**: 自动检测应用更新

## 技术架构

### 前端技术栈
- **框架**: UniApp (Vue.js)
- **状态管理**: Vuex
- **UI组件**: 自定义组件 + uParse富文本解析
- **样式**: CSS3 + SCSS
- **网络请求**: uni.request

### 项目结构
```
uni-mall/
├── pages/                 # 页面文件
│   ├── index/            # 首页
│   ├── catalog/          # 商品分类
│   ├── goods/            # 商品详情
│   ├── cart/             # 购物车
│   ├── shopping/         # 订单确认
│   ├── ucenter/          # 用户中心
│   ├── auth/             # 用户认证
│   ├── brand/            # 品牌展示
│   ├── topic/            # 专题活动
│   ├── search/           # 搜索功能
│   └── appoint/          # 预约咨询
├── components/           # 公共组件
│   ├── show-empty/       # 空状态组件
│   └── uParse/           # 富文本解析组件
├── utils/                # 工具函数
│   ├── api.js           # API接口定义
│   └── util.js          # 通用工具函数
├── store/                # Vuex状态管理
├── static/               # 静态资源
├── common/               # 公共样式
├── App.vue              # 应用入口
├── main.js              # 主入口文件
├── pages.json           # 页面配置
├── manifest.json        # 应用配置
└── uni.scss             # 全局样式变量
```

### 核心配置

#### 应用配置 (manifest.json)
- **应用名称**: 千合商城
- **应用ID**: __UNI__6B1306B
- **版本**: 1.0.0
- **支持平台**: 微信小程序、H5、APP
- **微信小程序AppID**: wxc875d1b8c2866a2f

#### 页面配置 (pages.json)
- **首页**: 千合商城首页，支持下拉刷新
- **分类页**: 商品分类展示
- **用户中心**: 个人中心页面
- **订单管理**: 订单列表和详情
- **地址管理**: 收货地址管理

## API接口

### 商品相关接口
- `shop/newGoods` - 新品首发
- `shop/hotGoods` - 热卖商品
- `shop/goodsDetail` - 商品详情
- `shop/goodlist` - 商品列表
- `shop/catalog` - 分类目录

### 购物车接口
- `shop/cart` - 购物车列表
- `shop/addCart` - 添加购物车
- `shop/updateChart` - 更新购物车
- `shop/deleteChart` - 删除购物车商品

### 订单接口
- `shop/submitOrder` - 提交订单
- `shop/orderlist` - 订单列表
- `shop/orderdetail` - 订单详情
- `shop/cancelOrder` - 取消订单

### 用户接口
- `auth/login` - 用户登录
- `auth/register` - 用户注册
- `shop/getUserAddressData` - 地址列表
- `shop/saveAddress` - 保存地址

## 开发环境

### 环境要求
- Node.js >= 12.0.0
- HBuilderX (推荐) 或 VS Code
- 微信开发者工具 (小程序开发)

### 安装依赖
```bash
# 如果项目有package.json文件
npm install

# 或者使用yarn
yarn install
```

### 开发运行
1. **HBuilderX开发**:
   - 打开HBuilderX
   - 导入项目
   - 选择运行到对应平台

2. **微信小程序开发**:
   - 在微信开发者工具中导入项目
   - 配置AppID
   - 编译运行

3. **H5开发**:
   - 运行到浏览器
   - 支持热重载

### 构建发布
1. **小程序发布**:
   - 在微信开发者工具中上传代码
   - 提交审核发布

2. **APP发布**:
   - 在HBuilderX中云打包
   - 生成APK/IPA文件

## 项目特色

### 🎯 用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **流畅动画**: 页面切换和交互动画
- **加载优化**: 图片懒加载、分页加载
- **错误处理**: 完善的错误提示和异常处理

### 🔧 技术亮点
- **跨平台兼容**: 一套代码多端运行
- **模块化设计**: 组件化开发，代码复用性高
- **状态管理**: 使用Vuex统一管理应用状态
- **网络监控**: 实时监控网络状态变化

### 📊 性能优化
- **图片优化**: 图片压缩和CDN加速
- **代码分割**: 按需加载，减少包体积
- **缓存策略**: 合理使用本地存储
- **请求优化**: 防抖节流，避免重复请求

## 配置说明

### 服务器配置
在 `utils/util.js`
```
domain: 'http://localhost:8085/platform_main_web_war_exploded/',
spId: '4',
platAccountId: 'd9835e1b8f6e44fabdd4bf55128f9397',
```

### 微信配置
在 `manifest.json` 中配置微信相关参数：
- 微信小程序AppID
- 微信支付配置
- 微信登录配置

### 地图配置
配置高德地图API密钥：
```javascript
"maps": {
    "amap": {
        "appkey_ios": "",
        "appkey_android": ""
    }
}
```

## 更新日志

### v1.2.0 (最新版本)
- 🆕 **预约咨询功能**: 新增在线预约咨询服务页面
- 📅 **日期时间选择**: 支持灵活的日期和时间段选择
- 🎨 **中医养生主题**: 采用棕色系配色方案，营造温暖舒适的视觉体验
- 📱 **响应式设计**: 完美适配移动端，提供流畅的用户体验
- ✅ **表单验证**: 完善的表单验证和用户提示功能
- 🔧 **代码优化**: 模块化设计，易于维护和扩展

### v1.1.0
- 🎨 **UI设计升级**: 参考中医养生主题重新设计个人中心页面
- 🎨 **配色方案优化**: 采用棕色系配色方案 (#8B4513, #D2B48C, #FDF5E6)
- 📊 **数据统计展示**: 新增用户订单、优惠券、收藏数量统计卡片
- 🎯 **交互体验提升**: 优化按钮点击效果和页面布局
- 📱 **响应式设计**: 改进移动端适配和视觉效果
- 🔧 **代码结构优化**: 重构页面组件，提升代码可维护性

### v1.0.0 (基础版本)
- ✅ 完成基础电商功能开发
- ✅ 支持微信小程序、H5、APP多端运行
- ✅ 实现用户认证和订单管理
- ✅ 完成商品展示和购物车功能

## 预约咨询功能说明

### 功能特性
- **用户信息填写**: 姓名、手机号、推荐人验证码
- **日期选择**: 支持本周和下周的日期选择，自动禁用周末
- **时间段选择**: 提供多个时间段选择，显示剩余名额
- **需求描述**: 支持详细描述咨询需求
- **表单验证**: 完整的表单验证和错误提示
- **提交反馈**: 提交成功后自动返回上一页

### 设计特色
- **中医养生主题**: 采用温暖的棕色系配色方案
- **卡片式布局**: 现代化的卡片设计，层次分明
- **装饰元素**: 精致的SVG背景装饰
- **交互反馈**: 丰富的hover效果和状态变化

### 技术实现
- **Vue.js框架**: 基于UniApp的Vue.js开发
- **响应式设计**: 使用rpx单位适配不同屏幕
- **状态管理**: 完善的数据绑定和状态管理
- **表单处理**: 完整的表单验证和提交逻辑

## 常见问题

### Q: 如何修改服务器地址？
A: 在 `utils/util.js` 文件中修改 `domain` 变量。

### Q: 如何添加新的页面？
A: 在 `pages` 目录下创建新页面，并在 `pages.json` 中配置路由。

### Q: 如何自定义样式？
A: 修改 `uni.scss` 文件中的全局样式变量，或在对应页面中编写样式。

### Q: 如何处理网络请求？
A: 使用 `utils/util.js` 中的 `request` 方法，支持Promise和错误处理。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: 千合商城开发团队
- 邮箱: [联系邮箱]
- 项目地址: [项目仓库地址]

---

**注意**: 本项目仅供学习和参考使用，商业使用请确保遵守相关法律法规和平台政策。