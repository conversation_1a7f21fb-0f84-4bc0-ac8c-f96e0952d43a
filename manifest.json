{
    "name" : "千合商城",
    "appid" : "__UNI__6B1306B",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : 3,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : false,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Share" : {},
            "VideoPlayer" : {},
            "Webview-x5" : {},
            "Messaging" : {},
            "OAuth" : {},
            "Push" : {},
            "Payment" : {},
            "LivePusher" : {},
            "Geolocation" : {},
            "Maps" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "idfa" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:demo.com" ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "",
                        "appsecret" : "",
                        "UniversalLinks" : ""
                    },
                    "apple" : {}
                },
                "push" : {
                    "unipush" : {}
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "",
                        "UniversalLinks" : ""
                    },
                    "appleiap" : {},
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "",
                        "UniversalLinks" : ""
                    }
                },
                "ad" : {
                    "360" : {},
                    "csj" : {},
                    "gdt" : {}
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common"
            }
        },
        "nvueLaunchMode" : "fast",
        "uniStatistics" : {
            "enable" : true
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxc875d1b8c2866a2f",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于千合商城"
            }
        },
        "plugins" : {
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        },
        "appid" : ""
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "title" : "千合商城-uniapp版",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : ""
                }
            }
        },
        "domain" : "",
        "router" : {
            "mode" : "hash",
            "base" : "/h5/"
        },
        "uniStatistics" : {
            "enable" : true
        },
        "devServer" : {
            "https" : false
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        }
    }
}
