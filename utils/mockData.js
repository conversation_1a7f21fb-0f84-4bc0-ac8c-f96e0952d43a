// Mock Database
const mockDatabase = {
    getTopCategories: () => [
        { goodsClassCode: 'cat-fruit-flower', goodsClassName: '水果鲜花' },
        { goodsClassCode: 'cat-vegetable-soy', goodsClassName: '蔬菜豆制品' },
        { goodsClassCode: 'cat-meat-egg', goodsClassName: '肉禽蛋' },
        { goodsClassCode: 'cat-seafood', goodsClassName: '海鲜水产' },
        { goodsClassCode: 'cat-grain-oil', goodsClassName: '粮油调味' },
        { goodsClassCode: 'cat-cooked-food', goodsClassName: '熟食' },
    ],
    getSidebarCategories: (parentId) => {
        const categories = {
            'cat-fruit-flower': [
                { itemLabelId: 'sub-fruit-1', labelTitle: '今日推荐' },
                { itemLabelId: 'sub-fruit-2', labelTitle: '时令上新' },
                { itemLabelId: 'sub-fruit-3', labelTitle: '鲜花绿植' },
                { itemLabelId: 'sub-fruit-4', labelTitle: '超值特惠' },
                { itemLabelId: 'sub-fruit-5', labelTitle: '葡提/圣女果' },
                { itemLabelId: 'sub-fruit-6', labelTitle: '西瓜/蜜瓜' },
                { itemLabelId: 'sub-fruit-7', labelTitle: '榴莲/山竹' },
                { itemLabelId: 'sub-fruit-8', labelTitle: '桃李杏梅' },
                { itemLabelId: 'sub-fruit-9', labelTitle: '鲜果切' },
                { itemLabelId: 'sub-fruit-10', labelTitle: '蓝莓/奇异果' },
                { itemLabelId: 'sub-fruit-11', labelTitle: '苹果/梨/香蕉' },
            ],
            // 其他顶层分类的侧边栏分类可以根据需要补充
        };
        return categories[parentId] || [];
    },
    getGoods: (categoryId) => {
        const goods = {
            'sub-fruit-1': [
                {
                    fbaInventoryId: 'p-101',
                    productionName: '龙泉水蜜桃约500g/份',
                    listingPrice: 10.9,
                    originalPrice: 13.9,
                    sales: 150,
                    smallImage: 'https://example.com/images/peach.jpg',
                    tags: ['时令'],
                    description: '桃香扑鼻 | 软吃爆汁 | 硬吃脆甜',
                    discountInfo: '满1件可换购',
                    weightPrice: '称重退差价¥10.9/500g',
                    liveInfo: '直播中 | 这里有0.01元...',
                },
                {
                    fbaInventoryId: 'p-102',
                    productionName: '优赐8424麒麟瓜约5.5kg/个',
                    listingPrice: 39.9,
                    originalPrice: 42.9,
                    sales: 80,
                    smallImage: 'https://example.com/images/watermelon.jpg',
                    tags: ['限时抢', '时令'],
                    description: '核心产区 | 产地直采 | 酥脆可口',
                    discountInfo: '库存1%',
                    specialLabel: '社群团购热销榜第1名',
                },
                {
                    fbaInventoryId: 'p-103',
                    productionName: '本地桂味荔枝500g±15g/份',
                    listingPrice: 29.9,
                    sales: 200,
                    smallImage: 'https://example.com/images/lychee.jpg',
                    description: '本地桂味 | 香甜多汁 | 应季荔枝',
                },
            ],
            // 其他侧边栏分类商品可根据需要补充
        };
        return goods[categoryId] || [];
    }
};

// Mock API request function
export const mockRequest = (action, params) => {
    return new Promise(resolve => {
        setTimeout(() => {
            let data;
            if (action === 'getTopCategories') {
                data = mockDatabase.getTopCategories();
            } else if (action === 'getSidebarCategories') {
                data = mockDatabase.getSidebarCategories(params.parentId);
            } else if (action === 'getGoods') {
                data = mockDatabase.getGoods(params.categoryId);
            }
            resolve(data);
        }, 300); // Simulate network delay
    });
};
